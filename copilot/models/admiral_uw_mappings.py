from __future__ import annotations

from operator import and_
from uuid import uuid4

from sqlalchemy import (
    BigInteger,
    Boolean,
    Column,
    ForeignKey,
    Index,
    String,
    UniqueConstraint,
    text,
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import foreign, relationship

from copilot.models import BaseModel  # type: ignore


class AdmiralAssignmentLog(BaseModel):
    __tablename__ = "admiral_assignment_logs"

    producer_no = Column(String, nullable=False, index=True)
    submission_id = Column(UUID(as_uuid=True), ForeignKey("submissions.id", ondelete="CASCADE"), nullable=False)
    broker_email = Column(String, nullable=True)
    assigned_underwriter_email = Column(String, nullable=False)
    sequence_number = Column(BigInteger, nullable=False, server_default=text("nextval('admiral_assignment_sequence')"))
    producer_sequence_no = Column(String, nullable=True)
    inbox = Column(String, nullable=True)

    __table_args__ = (
        # Index for round-robin lookup
        Index("ix_admiral_producer_uw_assignments", "producer_no", "assigned_underwriter_email"),
        # Index for idempotency check
        Index("ix_admiral_submission_assignments", "submission_id", "assigned_underwriter_email", unique=True),
    )


class AdmiralUWMappingAssignment(BaseModel):
    __tablename__ = "admiral_uw_mapping_assignments"

    uw_mapping_id = Column(UUID(as_uuid=True), ForeignKey("admiral_uw_mappings.id", ondelete="CASCADE"), nullable=False)
    user_email = Column(String, nullable=False)
    for_renewals = Column(Boolean, nullable=False)

    __table_args__ = (UniqueConstraint("uw_mapping_id", "user_email", "for_renewals"),)


class AdmiralUWMapping(BaseModel):
    __tablename__ = "admiral_uw_mappings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4)
    broker_first_name = Column(String, nullable=True)
    broker_last_name = Column(String, nullable=True)
    broker_email = Column(String, nullable=False, index=True)
    # Broker ID is optional, because we'll only be using it if we ever need to override the email matching
    broker_id = Column(
        UUID(as_uuid=True), ForeignKey("brokerage_employees.id", ondelete="CASCADE"), nullable=True, index=True
    )
    brokerage_name = Column(String, nullable=True)
    producer_no = Column(String, nullable=True)
    producer_sequence_no = Column(String, nullable=True)
    inbox = Column(String, nullable=True)

    renewal_uws = relationship(
        "AdmiralUWMappingAssignment",
        primaryjoin=and_(
            foreign(AdmiralUWMappingAssignment.uw_mapping_id) == id,
            AdmiralUWMappingAssignment.for_renewals == True,
        ),
        cascade="all, delete-orphan",
        uselist=True,
        overlaps="non_renewal_uws, admiral_uw_mappings",
    )

    non_renewal_uws = relationship(
        "AdmiralUWMappingAssignment",
        primaryjoin=and_(
            foreign(AdmiralUWMappingAssignment.uw_mapping_id) == id,
            AdmiralUWMappingAssignment.for_renewals == False,
        ),
        cascade="all, delete-orphan",
        uselist=True,
        overlaps="renewal_uws, admiral_uw_mappings",
    )

from sqlalchemy import Column, Index, String, UniqueConstraint

from copilot.models import BaseModel


class AdmiralIndividualAssignment(BaseModel):
    __tablename__ = "admiral_individual_assignments"

    broker_email = Column(String, nullable=False, index=True)
    inbox = Column(String, nullable=False, index=True)
    uw_email = Column(String, nullable=False)

    __table_args__ = (
        UniqueConstraint("broker_email", "inbox"),
        Index("ix_admiral_individual_broker_inbox", "broker_email", "inbox"),
    )

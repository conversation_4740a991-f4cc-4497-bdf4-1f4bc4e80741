"""Add Admiral Individual Assignments and update Admiral <PERSON><PERSON> Mappings

Revision ID: admiral_individual_assignments
Revises:
Create Date: 2025-06-02 17:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = "admiral_individual_assignments"
down_revision = None  # Replace with actual previous revision
branch_labels = None
depends_on = None


def upgrade():
    # Create admiral_individual_assignments table
    op.create_table(
        "admiral_individual_assignments",
        sa.Column("id", postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column("created_at", sa.DateTime(timezone=True), server_default=sa.text("now()"), nullable=True),
        sa.Column("updated_at", sa.DateTime(timezone=True), nullable=True),
        sa.Column("broker_email", sa.String(), nullable=False),
        sa.Column("inbox", sa.String(), nullable=False),
        sa.Column("uw_email", sa.String(), nullable=False),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("broker_email", "inbox"),
    )

    # Create indexes for admiral_individual_assignments
    op.create_index("ix_admiral_individual_broker_inbox", "admiral_individual_assignments", ["broker_email", "inbox"])
    op.create_index(
        op.f("ix_admiral_individual_assignments_broker_email"), "admiral_individual_assignments", ["broker_email"]
    )
    op.create_index(op.f("ix_admiral_individual_assignments_inbox"), "admiral_individual_assignments", ["inbox"])

    # Add inbox column to admiral_uw_mappings
    op.add_column("admiral_uw_mappings", sa.Column("inbox", sa.String(), nullable=True))

    # Add producer_sequence_no and inbox columns to admiral_assignment_logs
    op.add_column("admiral_assignment_logs", sa.Column("producer_sequence_no", sa.String(), nullable=True))
    op.add_column("admiral_assignment_logs", sa.Column("inbox", sa.String(), nullable=True))


def downgrade():
    # Remove columns from admiral_assignment_logs
    op.drop_column("admiral_assignment_logs", "inbox")
    op.drop_column("admiral_assignment_logs", "producer_sequence_no")

    # Remove inbox column from admiral_uw_mappings
    op.drop_column("admiral_uw_mappings", "inbox")

    # Drop indexes for admiral_individual_assignments
    op.drop_index(op.f("ix_admiral_individual_assignments_inbox"), table_name="admiral_individual_assignments")
    op.drop_index(op.f("ix_admiral_individual_assignments_broker_email"), table_name="admiral_individual_assignments")
    op.drop_index("ix_admiral_individual_broker_inbox", table_name="admiral_individual_assignments")

    # Drop admiral_individual_assignments table
    op.drop_table("admiral_individual_assignments")

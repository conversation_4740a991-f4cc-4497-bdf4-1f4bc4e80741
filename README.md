# Kalepa Copilot API

## Getting Started

1. Copy the `access token` from the `GitHub Kalepa-tech` account in 1Password.
2. Execute `GITHUB_TOKEN=<access token> ./ldc up -d app` to start a database, restore a snapshot of the development database, execute migrations, and start the Copilot API.
3. Use the `Copilot API` Postman collection to make requests to the server on port 8081. Dummy.

## Testing

### Behave tests

Behave tests are intended to be executed on clean state of database. If you want to run them locally please execute following to clean db state:

```
./ldc down 
```

after that you can execute tests with this command:

```
./ldc up --exit-code-from behave behave
```

Please note that with default .env.ci, flag INTERACT_WITH_EXTERNAL_RESOURCES is set to false so app will not interact
with external clients (for supported endpoints).

Important! Behave tests are run in limited environment on Github Actions. That's why uwsgi-dev.ini is configured to 1
process. If processes count is increased, some of the test will fail with `RemoteDisconnected` error.

### Unit tests

#### Locally
```
uv run --frozen pytest --envfile .env.ci
# or
( export $(grep -v '^#' .env.ci | xargs)  && uv run --frozen pytest )
```

#### In Docker

```
./ldc up --exit-code-from pytest-unit pytest-unit
```

# Running integration tests locally

## In Docker

```
./ldc up --exit-code-from pytest-integration pytest-integration
```

## Locally
#### Docker DB
```
./ldc up -d db-integration-tests
```
You can also run:
```
 ./ldc up migrate-db-integration-tests
 ```
This will migrate your Docker integration test DB to the latest version (also launch it first if necessary).

#### Local DB
Create a local database and set environment variable `SQLALCHEMY_TEST_COPILOT_DB_URI` to the connection string of the created
database (e.g. `postgresql://copilot:copilot@localhost:5435/copilotapi`). Run the tests from PyCharm as other unit tests.

## Local Development Handbook

An invaluable resource to assist you in your development tasks is the Local Development Handbook. This compendium showcases all the preferred practices and instrumental tools employed across our repositories.

As you commence work on any repository, it is strongly recommended to first peruse this Handbook. The content is curated to address a majority of the questions that could potentially arise and provides solution-oriented approaches to tackle common issues encountered during development.

The Local Development Handbook can be accessed via this [**link**](https://kalepa.atlassian.net/wiki/spaces/ON/pages/2242576389/Local+Development+Handbook). Be sure to use it as a reliable reference guide, supporting your seamless integration into our development processes.

## Dependency Management

The `uv.lock` file, being part of the repository, necessitates updating whenever alterations are made to the dependencies in `pyproject.toml`. Several approaches exist to facilitate this process of updating dependencies:

* To introduce a new package, rather than manually specifying it in the `pyproject.toml`, you can employ the `uv add <package_name>` command. This action will automatically revise both `pyproject.toml` and `uv.lock` files.
* If you prefer to directly alter the `pyproject.toml`, or if there is a need to modify a dependency version, the `uv sync --all-extras` command is at your disposal. This command will auto-update the lock file, aligning it with the latest versions of all dependencies. If you wish only to see your manual changes reflected in the lock file, without influencing other dependencies, the `uv lock` command will achieve this.
* To update dependencies to their most recent matching versions, `uv sync --all-extras` will do the trick. You can also use the `-n` uv flag to disregard local cache and fetch new package versions from the repository. The key distinction between `uv lock` and `uv sync` is that while the former solely updates the lock file, the latter also affects packages on your local system. 

**It is crucial to commit the updated lock file each time you modify project dependencies.**

Moreover, we encourage a periodic update of all packages to ensure we are utilizing the most recent versions of all dependencies, complete with the latest security patches. Aim to execute `uv sync` every few days when working on a project and include the revised lock file with your changes. Rest assured, should any issues arise, we always have the option to revert to the lock file's previous version.

## Code formatting
This repository is configured to automatically check and fix code formatting issues using the `pre-commit` application. Your code is automatically checked in CI/CD after being pushed to GitHub, but you can configure your local environment to run those checks every time you commit. To do so, follow the steps below:
- First, make sure you have `pre-commit` installed on your computer: `unset VIRTUAL_ENV && python3 -m pip install pre-commit`
- Then, install pre-commit hooks in your local repository:
  - `pre-commit install`
  - Make sure to execute that command in every repository that supports pre-commit hooks (every repository that has the `.pre-commit-config.yaml` file).

And that's it. Every time you commit, checks will launch automatically and fix all issues they find. You can also run those checks manually using the following command: `pre-commit run --all-files`
